// 钉钉认证用户信息显示修复验证脚本
// 请在Chrome扩展的控制台中运行此脚本

console.log('🚀 开始验证钉钉认证用户信息显示修复效果...');

// 验证函数：检查认证状态
async function verifyAuthStatus() {
  console.log('\n1️⃣ 验证认证状态获取...');
  
  try {
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action: 'getDingTalkAuthStatus' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });

    if (response.success) {
      const data = response.data;
      console.log('✅ 认证状态获取成功');
      console.log('📊 状态详情:', {
        isAuthenticated: data.isAuthenticated,
        hasUserInfo: !!data.userInfo,
        userName: data.userInfo?.name,
        userAvatar: data.userInfo?.avatar,
        organizationCount: data.organizations?.length || 0,
        selectedOrg: data.selectedOrganization?.corpName
      });
      
      return {
        success: true,
        isAuthenticated: data.isAuthenticated,
        userInfoComplete: !!(data.userInfo?.name && data.userInfo.name !== '钉钉用户'),
        data
      };
    } else {
      console.log('❌ 认证状态获取失败:', response.error);
      return { success: false, error: response.error };
    }
  } catch (error) {
    console.log('❌ 验证认证状态失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 验证函数：测试强制刷新
async function verifyForceRefresh() {
  console.log('\n2️⃣ 验证强制刷新功能...');
  
  try {
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action: 'refreshDingTalkAuth' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });

    if (response.success) {
      const data = response.data;
      console.log('✅ 强制刷新成功');
      console.log('📊 刷新后状态:', {
        isAuthenticated: data.isAuthenticated,
        userName: data.userInfo?.name,
        userInfoImproved: !!(data.userInfo?.name && data.userInfo.name !== '钉钉用户')
      });
      
      return {
        success: true,
        userInfoComplete: !!(data.userInfo?.name && data.userInfo.name !== '钉钉用户'),
        data
      };
    } else {
      console.log('❌ 强制刷新失败:', response.error);
      return { success: false, error: response.error };
    }
  } catch (error) {
    console.log('❌ 验证强制刷新失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 验证函数：检查存储数据
async function verifyStorageData() {
  console.log('\n3️⃣ 验证存储数据...');
  
  try {
    const keys = ['Login', 'MY_ORGS_KEY', 'selectedCorpId', 'userSettings', 'dingtalk_auth_timestamp'];
    const data = await chrome.storage.local.get(keys);
    
    console.log('✅ 存储数据获取成功');
    console.log('📊 存储内容:', {
      loginStatus: data.Login,
      hasUserSettings: !!data.userSettings,
      userSettingsName: data.userSettings?.name,
      organizationCount: data.MY_ORGS_KEY?.length || 0,
      selectedCorpId: data.selectedCorpId,
      authTimestamp: data.dingtalk_auth_timestamp ? new Date(data.dingtalk_auth_timestamp).toLocaleString() : '无'
    });
    
    return {
      success: true,
      hasValidData: !!(data.Login && data.userSettings),
      userInfoInStorage: !!(data.userSettings?.name && data.userSettings.name !== '钉钉用户'),
      data
    };
  } catch (error) {
    console.log('❌ 验证存储数据失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 验证函数：检查UI更新机制
async function verifyUIUpdate() {
  console.log('\n4️⃣ 验证UI更新机制...');
  
  try {
    // 检查侧边栏是否存在
    const sidebarExists = !!document.querySelector('#authName') || 
                         !!document.querySelector('.auth-name') ||
                         !!window.sidebarApp;
    
    if (sidebarExists) {
      console.log('✅ 侧边栏UI元素检测成功');
      
      // 如果在侧边栏环境中，检查UI元素
      const authName = document.querySelector('#authName');
      const authAvatar = document.querySelector('#authAvatar');
      const authOrg = document.querySelector('#authOrg');
      
      if (authName || authAvatar || authOrg) {
        console.log('📊 UI元素状态:', {
          hasNameElement: !!authName,
          nameContent: authName?.textContent,
          hasAvatarElement: !!authAvatar,
          avatarContent: authAvatar?.innerHTML?.substring(0, 50),
          hasOrgElement: !!authOrg,
          orgContent: authOrg?.textContent
        });
        
        return {
          success: true,
          uiElementsFound: true,
          userInfoDisplayed: !!(authName?.textContent && authName.textContent !== '钉钉用户')
        };
      } else {
        console.log('⚠️ UI元素未找到，可能不在侧边栏环境中');
        return { success: true, uiElementsFound: false };
      }
    } else {
      console.log('ℹ️ 不在侧边栏环境中，跳过UI检查');
      return { success: true, uiElementsFound: false };
    }
  } catch (error) {
    console.log('❌ 验证UI更新失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 主验证函数
async function runVerification() {
  console.log('🔍 开始运行完整验证...\n');
  
  const results = {
    authStatus: await verifyAuthStatus(),
    forceRefresh: await verifyForceRefresh(),
    storageData: await verifyStorageData(),
    uiUpdate: await verifyUIUpdate()
  };
  
  console.log('\n📋 验证结果汇总:');
  console.log('==================');
  
  // 分析结果
  const authWorking = results.authStatus.success;
  const refreshWorking = results.forceRefresh.success;
  const storageWorking = results.storageData.success;
  const uiWorking = results.uiUpdate.success;
  
  const userInfoComplete = results.forceRefresh.userInfoComplete || 
                          results.authStatus.userInfoComplete ||
                          results.storageData.userInfoInStorage;
  
  console.log('✅ 认证状态获取:', authWorking ? '正常' : '异常');
  console.log('✅ 强制刷新功能:', refreshWorking ? '正常' : '异常');
  console.log('✅ 存储数据管理:', storageWorking ? '正常' : '异常');
  console.log('✅ UI更新机制:', uiWorking ? '正常' : '异常');
  console.log('✅ 用户信息完整性:', userInfoComplete ? '完整' : '不完整');
  
  // 总体评估
  if (authWorking && refreshWorking && storageWorking && userInfoComplete) {
    console.log('\n🎉 验证通过！修复效果良好，用户信息显示应该正常工作。');
    console.log('💡 建议：可以进行实际的登录测试来进一步验证。');
  } else if (authWorking && refreshWorking && storageWorking && !userInfoComplete) {
    console.log('\n⚠️ 部分验证通过，但用户信息仍不完整。');
    console.log('💡 建议：检查钉钉API响应格式或网络连接。');
  } else {
    console.log('\n❌ 验证发现问题，需要进一步调试。');
    console.log('💡 建议：检查控制台错误信息和网络请求。');
  }
  
  // 提供调试建议
  console.log('\n🔧 调试建议:');
  if (!authWorking) {
    console.log('- 检查background script是否正常运行');
    console.log('- 验证消息传递机制是否工作');
  }
  if (!userInfoComplete) {
    console.log('- 检查钉钉API响应格式');
    console.log('- 验证Cookie是否有效');
    console.log('- 查看网络请求是否成功');
  }
  if (!uiWorking && results.uiUpdate.uiElementsFound) {
    console.log('- 检查UI更新逻辑');
    console.log('- 验证事件监听器是否正常');
  }
  
  return results;
}

// 快速测试函数
async function quickTest() {
  console.log('⚡ 运行快速测试...\n');
  
  const authResult = await verifyAuthStatus();
  
  if (authResult.success && authResult.isAuthenticated) {
    if (authResult.userInfoComplete) {
      console.log('🎉 快速测试通过！用户信息显示正常。');
    } else {
      console.log('⚠️ 用户已认证但信息不完整，尝试刷新...');
      const refreshResult = await verifyForceRefresh();
      
      if (refreshResult.success && refreshResult.userInfoComplete) {
        console.log('🎉 刷新后用户信息已完整！');
      } else {
        console.log('❌ 刷新后用户信息仍不完整，需要进一步调试。');
      }
    }
  } else if (authResult.success && !authResult.isAuthenticated) {
    console.log('ℹ️ 用户未认证，请先登录钉钉。');
  } else {
    console.log('❌ 快速测试失败，请检查扩展状态。');
  }
  
  return authResult;
}

// 导出函数供手动调用
window.verifyDingTalkFix = {
  runVerification,
  quickTest,
  verifyAuthStatus,
  verifyForceRefresh,
  verifyStorageData,
  verifyUIUpdate
};

// 自动运行快速测试
console.log('🎯 自动运行快速测试...');
quickTest().then(() => {
  console.log('\n💡 提示：');
  console.log('- 运行 verifyDingTalkFix.runVerification() 进行完整验证');
  console.log('- 运行 verifyDingTalkFix.quickTest() 进行快速测试');
  console.log('- 各个验证函数也可以单独调用');
}).catch(error => {
  console.error('❌ 自动测试失败:', error);
});
