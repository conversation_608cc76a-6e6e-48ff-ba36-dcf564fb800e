<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证状态调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .debug-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .debug-label {
            font-weight: bold;
            color: #333;
            display: inline-block;
            min-width: 150px;
        }
        .debug-value {
            color: #666;
            margin-left: 10px;
        }
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .success {
            border-left-color: #4caf50;
            background: #e8f5e8;
        }
        .warning {
            border-left-color: #ff9800;
            background: #fff3e0;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1565c0;
        }
        pre {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .json-viewer {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>钉钉认证状态调试工具</h1>
    
    <div class="debug-section">
        <h2>快速操作</h2>
        <button onclick="checkAllStatus()">检查所有状态</button>
        <button onclick="checkStorage()">检查存储数据</button>
        <button onclick="checkCookies()">检查Cookies</button>
        <button onclick="refreshAuthStatus()">刷新认证状态</button>
        <button onclick="clearDebugLog()">清空日志</button>
    </div>

    <div class="debug-section">
        <h2>认证状态概览</h2>
        <div id="authOverview">正在检查...</div>
    </div>

    <div class="debug-section">
        <h2>存储数据详情</h2>
        <div id="storageDetails">正在加载...</div>
    </div>

    <div class="debug-section">
        <h2>Cookie信息</h2>
        <div id="cookieDetails">正在加载...</div>
    </div>

    <div class="debug-section">
        <h2>调试日志</h2>
        <div id="debugLog"></div>
    </div>

    <script>
        let debugLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push({ timestamp, message, type });
            updateDebugLog();
            console.log(`[${timestamp}] ${message}`);
        }

        function updateDebugLog() {
            const logElement = document.getElementById('debugLog');
            logElement.innerHTML = debugLog.map(entry => 
                `<div class="debug-item ${entry.type}">
                    <span class="debug-label">${entry.timestamp}</span>
                    <span class="debug-value">${entry.message}</span>
                </div>`
            ).join('');
        }

        function clearDebugLog() {
            debugLog = [];
            updateDebugLog();
        }

        async function checkAllStatus() {
            log('开始全面检查认证状态...');
            await Promise.all([
                checkAuthStatus(),
                checkStorage(),
                checkCookies()
            ]);
            log('全面检查完成', 'success');
        }

        async function checkAuthStatus() {
            try {
                log('检查认证状态...');
                
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ action: 'getDingTalkAuthStatus' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                const overviewElement = document.getElementById('authOverview');
                
                if (response.success) {
                    const data = response.data;
                    
                    overviewElement.innerHTML = `
                        <div class="debug-item ${data.isAuthenticated ? 'success' : 'warning'}">
                            <span class="debug-label">认证状态:</span>
                            <span class="debug-value">${data.isAuthenticated ? '✅ 已认证' : '❌ 未认证'}</span>
                        </div>
                        <div class="debug-item">
                            <span class="debug-label">用户信息:</span>
                            <span class="debug-value">${data.userInfo ? `${data.userInfo.name || '无名称'} (${data.userInfo.userId || '无ID'})` : '无'}</span>
                        </div>
                        <div class="debug-item">
                            <span class="debug-label">用户头像:</span>
                            <span class="debug-value">${data.userInfo?.avatar ? '有头像' : '无头像'}</span>
                        </div>
                        <div class="debug-item">
                            <span class="debug-label">组织数量:</span>
                            <span class="debug-value">${data.organizations ? data.organizations.length : 0}</span>
                        </div>
                        <div class="debug-item">
                            <span class="debug-label">选中组织:</span>
                            <span class="debug-value">${data.selectedOrganization ? data.selectedOrganization.corpName : '无'}</span>
                        </div>
                        <div class="debug-item">
                            <span class="debug-label">环境:</span>
                            <span class="debug-value">${data.environment || '未知'}</span>
                        </div>
                        ${data.statusWarning ? `
                        <div class="debug-item warning">
                            <span class="debug-label">警告:</span>
                            <span class="debug-value">${data.statusWarning}</span>
                        </div>
                        ` : ''}
                        <div class="json-viewer">
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                    
                    log(`认证状态: ${data.isAuthenticated ? '已认证' : '未认证'}`, data.isAuthenticated ? 'success' : 'warning');
                    if (data.userInfo) {
                        log(`用户信息: ${data.userInfo.name || '无名称'}`, 'info');
                    } else {
                        log('用户信息缺失', 'error');
                    }
                } else {
                    overviewElement.innerHTML = `<div class="debug-item error">获取认证状态失败: ${response.error}</div>`;
                    log(`获取认证状态失败: ${response.error}`, 'error');
                }

            } catch (error) {
                log(`检查认证状态失败: ${error.message}`, 'error');
                document.getElementById('authOverview').innerHTML = 
                    `<div class="debug-item error">检查认证状态失败: ${error.message}</div>`;
            }
        }

        async function checkStorage() {
            try {
                log('检查存储数据...');
                
                const keys = ['Login', 'MY_ORGS_KEY', 'selectedCorpId', 'userSettings', 'dingtalk_auth_timestamp'];
                const data = await chrome.storage.local.get(keys);
                
                const storageElement = document.getElementById('storageDetails');
                storageElement.innerHTML = `
                    <div class="debug-item">
                        <span class="debug-label">Login状态:</span>
                        <span class="debug-value">${data.Login ? '✅ true' : '❌ false/undefined'}</span>
                    </div>
                    <div class="debug-item">
                        <span class="debug-label">用户设置:</span>
                        <span class="debug-value">${data.userSettings ? '有数据' : '无数据'}</span>
                    </div>
                    <div class="debug-item">
                        <span class="debug-label">组织数据:</span>
                        <span class="debug-value">${data.MY_ORGS_KEY ? `${data.MY_ORGS_KEY.length} 个组织` : '无数据'}</span>
                    </div>
                    <div class="debug-item">
                        <span class="debug-label">选中组织ID:</span>
                        <span class="debug-value">${data.selectedCorpId || '无'}</span>
                    </div>
                    <div class="debug-item">
                        <span class="debug-label">认证时间戳:</span>
                        <span class="debug-value">${data.dingtalk_auth_timestamp ? new Date(data.dingtalk_auth_timestamp).toLocaleString() : '无'}</span>
                    </div>
                    <div class="json-viewer">
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
                
                log('存储数据检查完成', 'success');
                
                // 检查用户设置的具体内容
                if (data.userSettings) {
                    log(`用户设置内容: ${JSON.stringify(data.userSettings)}`, 'info');
                } else {
                    log('用户设置为空，这可能是问题所在', 'warning');
                }
                
            } catch (error) {
                log(`检查存储数据失败: ${error.message}`, 'error');
                document.getElementById('storageDetails').innerHTML = 
                    `<div class="debug-item error">检查存储数据失败: ${error.message}</div>`;
            }
        }

        async function checkCookies() {
            try {
                log('检查Cookies...');
                
                const cookies = await chrome.cookies.getAll({ domain: '.dingtalk.com' });
                const accountCookie = cookies.find(c => c.name === 'account');
                
                const cookieElement = document.getElementById('cookieDetails');
                cookieElement.innerHTML = `
                    <div class="debug-item">
                        <span class="debug-label">Cookie总数:</span>
                        <span class="debug-value">${cookies.length}</span>
                    </div>
                    <div class="debug-item ${accountCookie ? 'success' : 'error'}">
                        <span class="debug-label">account Cookie:</span>
                        <span class="debug-value">${accountCookie ? '✅ 存在' : '❌ 不存在'}</span>
                    </div>
                    ${accountCookie ? `
                    <div class="debug-item">
                        <span class="debug-label">Cookie值:</span>
                        <span class="debug-value">${accountCookie.value.substring(0, 50)}...</span>
                    </div>
                    <div class="debug-item">
                        <span class="debug-label">过期时间:</span>
                        <span class="debug-value">${new Date(accountCookie.expirationDate * 1000).toLocaleString()}</span>
                    </div>
                    ` : ''}
                    <div class="json-viewer">
                        <pre>${JSON.stringify(cookies, null, 2)}</pre>
                    </div>
                `;
                
                log(`找到 ${cookies.length} 个钉钉相关Cookie`, 'success');
                if (accountCookie) {
                    log('account Cookie存在，认证应该有效', 'success');
                } else {
                    log('account Cookie不存在，用户可能未登录', 'warning');
                }
                
            } catch (error) {
                log(`检查Cookies失败: ${error.message}`, 'error');
                document.getElementById('cookieDetails').innerHTML = 
                    `<div class="debug-item error">检查Cookies失败: ${error.message}</div>`;
            }
        }

        async function refreshAuthStatus() {
            try {
                log('刷新认证状态...');
                
                const response = await new Promise((resolve, reject) => {
                    chrome.runtime.sendMessage({ action: 'refreshDingTalkAuth' }, (response) => {
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                if (response.success) {
                    log('认证状态刷新成功', 'success');
                    await checkAuthStatus(); // 重新检查状态
                } else {
                    log(`认证状态刷新失败: ${response.error}`, 'error');
                }
                
            } catch (error) {
                log(`刷新认证状态失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动检查
        document.addEventListener('DOMContentLoaded', () => {
            log('调试工具加载完成，开始自动检查...');
            checkAllStatus();
        });
    </script>
</body>
</html>
