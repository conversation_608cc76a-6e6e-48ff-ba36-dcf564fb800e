// 钉钉认证用户信息显示修复 - 实际测试脚本
// 请在Chrome扩展的侧边栏或任何扩展页面的控制台中运行

console.log('🚀 开始实际测试钉钉认证用户信息显示修复效果...');
console.log('📅 测试时间:', new Date().toLocaleString());

// 全局测试结果存储
window.testResults = {
  timestamp: Date.now(),
  tests: [],
  summary: {}
};

// 添加测试结果
function addTestResult(testName, success, details, recommendations = []) {
  const result = {
    name: testName,
    success,
    details,
    recommendations,
    timestamp: Date.now()
  };
  
  window.testResults.tests.push(result);
  
  const icon = success ? '✅' : '❌';
  console.log(`${icon} ${testName}: ${success ? '通过' : '失败'}`);
  if (details) console.log(`   详情: ${details}`);
  if (recommendations.length > 0) {
    console.log(`   建议: ${recommendations.join(', ')}`);
  }
}

// 测试1: 基础认证状态检查
async function test1_BasicAuthCheck() {
  console.log('\n🔍 测试1: 基础认证状态检查');
  
  try {
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action: 'getDingTalkAuthStatus' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });

    if (response && response.success) {
      const data = response.data;
      const isAuthenticated = data.isAuthenticated;
      const hasUserInfo = !!(data.userInfo && data.userInfo.name);
      const userInfoComplete = hasUserInfo && data.userInfo.name !== '钉钉用户';
      
      addTestResult(
        '基础认证状态检查',
        true,
        `认证状态: ${isAuthenticated ? '已认证' : '未认证'}, 用户信息: ${userInfoComplete ? '完整' : hasUserInfo ? '不完整' : '缺失'}`,
        isAuthenticated && !userInfoComplete ? ['尝试刷新认证状态', '检查API响应'] : []
      );
      
      return { success: true, isAuthenticated, userInfoComplete, data };
    } else {
      addTestResult('基础认证状态检查', false, `API调用失败: ${response?.error || '未知错误'}`, ['检查background script', '重新加载扩展']);
      return { success: false };
    }
  } catch (error) {
    addTestResult('基础认证状态检查', false, `异常: ${error.message}`, ['检查Chrome扩展权限', '重新加载扩展']);
    return { success: false };
  }
}

// 测试2: 强制刷新功能测试
async function test2_ForceRefreshTest() {
  console.log('\n🔄 测试2: 强制刷新功能测试');
  
  try {
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action: 'refreshDingTalkAuth' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });

    if (response && response.success) {
      const data = response.data;
      const userInfoComplete = !!(data.userInfo && data.userInfo.name && data.userInfo.name !== '钉钉用户');
      
      addTestResult(
        '强制刷新功能测试',
        true,
        `刷新成功, 用户信息: ${userInfoComplete ? '完整' : '不完整'}`,
        !userInfoComplete ? ['检查钉钉API连接', '验证Cookie有效性'] : []
      );
      
      return { success: true, userInfoComplete, data };
    } else {
      addTestResult('强制刷新功能测试', false, `刷新失败: ${response?.error || '未知错误'}`, ['检查网络连接', '重新登录钉钉']);
      return { success: false };
    }
  } catch (error) {
    addTestResult('强制刷新功能测试', false, `异常: ${error.message}`, ['检查扩展权限', '重新加载扩展']);
    return { success: false };
  }
}

// 测试3: UI元素检查
async function test3_UIElementCheck() {
  console.log('\n🖼️ 测试3: UI元素检查');
  
  try {
    // 检查是否在侧边栏环境中
    const authElements = {
      authName: document.querySelector('#authName'),
      authAvatar: document.querySelector('#authAvatar'),
      authOrg: document.querySelector('#authOrg'),
      authLoggedIn: document.querySelector('#authLoggedIn'),
      authNotLoggedIn: document.querySelector('#authNotLoggedIn')
    };
    
    const foundElements = Object.entries(authElements).filter(([key, element]) => element !== null);
    
    if (foundElements.length > 0) {
      const authName = authElements.authName;
      const userName = authName ? authName.textContent : '未找到';
      const userInfoDisplayed = userName && userName !== '钉钉用户' && userName !== '';
      
      addTestResult(
        'UI元素检查',
        true,
        `找到${foundElements.length}个UI元素, 显示用户名: "${userName}"`,
        !userInfoDisplayed ? ['检查UI更新逻辑', '验证数据绑定'] : []
      );
      
      return { success: true, elementsFound: foundElements.length, userName, userInfoDisplayed };
    } else {
      addTestResult('UI元素检查', true, '未在当前页面找到认证UI元素（可能不在侧边栏环境中）', ['在侧边栏页面运行此测试']);
      return { success: true, elementsFound: 0 };
    }
  } catch (error) {
    addTestResult('UI元素检查', false, `异常: ${error.message}`, ['检查页面DOM结构']);
    return { success: false };
  }
}

// 测试4: 存储数据验证
async function test4_StorageDataCheck() {
  console.log('\n💾 测试4: 存储数据验证');
  
  try {
    const keys = ['Login', 'userSettings', 'MY_ORGS_KEY', 'selectedCorpId', 'dingtalk_auth_timestamp'];
    const data = await chrome.storage.local.get(keys);
    
    const hasLogin = !!data.Login;
    const hasUserSettings = !!(data.userSettings && data.userSettings.name);
    const userSettingsComplete = hasUserSettings && data.userSettings.name !== '钉钉用户';
    const hasOrgs = !!(data.MY_ORGS_KEY && data.MY_ORGS_KEY.length > 0);
    
    addTestResult(
      '存储数据验证',
      true,
      `Login: ${hasLogin}, 用户设置: ${userSettingsComplete ? '完整' : hasUserSettings ? '不完整' : '缺失'}, 组织: ${hasOrgs ? data.MY_ORGS_KEY.length + '个' : '无'}`,
      !hasLogin ? ['重新登录钉钉'] : !userSettingsComplete ? ['检查API数据获取'] : []
    );
    
    return { success: true, hasLogin, userSettingsComplete, hasOrgs, data };
  } catch (error) {
    addTestResult('存储数据验证', false, `异常: ${error.message}`, ['检查存储权限']);
    return { success: false };
  }
}

// 测试5: Cookie状态检查
async function test5_CookieCheck() {
  console.log('\n🍪 测试5: Cookie状态检查');
  
  try {
    const cookies = await chrome.cookies.getAll({ domain: '.dingtalk.com' });
    const accountCookie = cookies.find(c => c.name === 'account');
    const hasCookie = !!accountCookie;
    const cookieValid = hasCookie && accountCookie.expirationDate > Date.now() / 1000;
    
    addTestResult(
      'Cookie状态检查',
      true,
      `钉钉Cookie: ${hasCookie ? '存在' : '不存在'}, 有效性: ${cookieValid ? '有效' : '无效或过期'}`,
      !cookieValid ? ['重新登录钉钉', '检查Cookie设置'] : []
    );
    
    return { success: true, hasCookie, cookieValid };
  } catch (error) {
    addTestResult('Cookie状态检查', false, `异常: ${error.message}`, ['检查Cookie权限']);
    return { success: false };
  }
}

// 综合测试报告
function generateTestReport(results) {
  console.log('\n📊 测试报告汇总');
  console.log('==================');
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const failedTests = totalTests - passedTests;
  
  console.log(`总测试数: ${totalTests}`);
  console.log(`通过: ${passedTests} ✅`);
  console.log(`失败: ${failedTests} ❌`);
  console.log(`成功率: ${Math.round(passedTests / totalTests * 100)}%`);
  
  // 分析问题和建议
  const allRecommendations = results.flatMap(r => r.recommendations);
  const uniqueRecommendations = [...new Set(allRecommendations)];
  
  if (uniqueRecommendations.length > 0) {
    console.log('\n💡 建议操作:');
    uniqueRecommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }
  
  // 保存到全局结果
  window.testResults.summary = {
    total: totalTests,
    passed: passedTests,
    failed: failedTests,
    successRate: Math.round(passedTests / totalTests * 100),
    recommendations: uniqueRecommendations
  };
  
  return window.testResults.summary;
}

// 主测试函数
async function runCompleteTest() {
  console.log('🎯 开始运行完整测试套件...\n');
  
  const results = [];
  
  // 运行所有测试
  results.push(await test1_BasicAuthCheck());
  results.push(await test2_ForceRefreshTest());
  results.push(await test3_UIElementCheck());
  results.push(await test4_StorageDataCheck());
  results.push(await test5_CookieCheck());
  
  // 生成报告
  const summary = generateTestReport(window.testResults.tests);
  
  // 最终评估
  console.log('\n🎯 最终评估:');
  if (summary.successRate >= 80) {
    console.log('🎉 修复效果良好！大部分功能正常工作。');
  } else if (summary.successRate >= 60) {
    console.log('⚠️ 修复部分有效，但仍有问题需要解决。');
  } else {
    console.log('❌ 修复效果不理想，需要进一步调试。');
  }
  
  console.log('\n📋 测试结果已保存到 window.testResults');
  console.log('💡 可以运行 console.table(window.testResults.tests) 查看详细结果');
  
  return window.testResults;
}

// 快速测试函数
async function runQuickTest() {
  console.log('⚡ 运行快速测试...\n');
  
  const authResult = await test1_BasicAuthCheck();
  
  if (authResult.success && authResult.isAuthenticated) {
    if (authResult.userInfoComplete) {
      console.log('🎉 快速测试通过！用户信息显示正常。');
      return true;
    } else {
      console.log('⚠️ 用户已认证但信息不完整，尝试刷新...');
      const refreshResult = await test2_ForceRefreshTest();
      
      if (refreshResult.success && refreshResult.userInfoComplete) {
        console.log('🎉 刷新后用户信息已完整！修复生效。');
        return true;
      } else {
        console.log('❌ 刷新后用户信息仍不完整，需要进一步调试。');
        return false;
      }
    }
  } else if (authResult.success && !authResult.isAuthenticated) {
    console.log('ℹ️ 用户未认证，请先登录钉钉后再测试。');
    return null;
  } else {
    console.log('❌ 快速测试失败，请检查扩展状态。');
    return false;
  }
}

// 导出测试函数
window.dingTalkFixTest = {
  runCompleteTest,
  runQuickTest,
  test1_BasicAuthCheck,
  test2_ForceRefreshTest,
  test3_UIElementCheck,
  test4_StorageDataCheck,
  test5_CookieCheck
};

// 自动运行快速测试
console.log('🎯 自动运行快速测试...');
runQuickTest().then(result => {
  if (result === true) {
    console.log('\n✨ 建议：修复效果良好，可以正常使用！');
  } else if (result === false) {
    console.log('\n🔧 建议：运行 dingTalkFixTest.runCompleteTest() 进行详细诊断');
  } else {
    console.log('\n📝 建议：请先登录钉钉，然后重新运行测试');
  }
  
  console.log('\n🛠️ 可用命令：');
  console.log('- dingTalkFixTest.runQuickTest() // 快速测试');
  console.log('- dingTalkFixTest.runCompleteTest() // 完整测试');
  console.log('- console.table(window.testResults.tests) // 查看测试结果');
}).catch(error => {
  console.error('❌ 自动测试失败:', error);
  console.log('🔧 请手动运行: dingTalkFixTest.runCompleteTest()');
});
