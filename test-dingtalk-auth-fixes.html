<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉认证修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button.danger { background-color: #dc3545; }
        button.danger:hover { background-color: #c82333; }
        
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .debug-info {
            background-color: #f1f3f4;
            border-left: 4px solid #ff4444;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .auth-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 6px;
            background-color: #f8f9fa;
        }
        
        .auth-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }
        .auth-icon.logged-in { background-color: #28a745; }
        .auth-icon.logged-out { background-color: #dc3545; }
        
        .cookie-simulator {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        
        input, select {
            padding: 6px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔧 钉钉认证修复功能测试</h1>
    
    <div class="container">
        <h2>📋 测试概述</h2>
        <p>本页面用于测试钉钉认证登录状态更新机制的修复效果，包括：</p>
        <ul>
            <li>✅ Cookie监听器触发条件修复</li>
            <li>✅ 状态保存后通知机制修复</li>
            <li>✅ 存储键定义一致性修复</li>
            <li>✅ 增强调试功能验证</li>
        </ul>
    </div>

    <div class="container">
        <h2>🎯 当前认证状态</h2>
        <div class="auth-status" id="authStatus">
            <div class="auth-icon logged-out" id="authIcon"></div>
            <div>
                <div id="authStatusText">未登录</div>
                <div id="authStatusDetail">等待认证状态检查...</div>
            </div>
        </div>
        <button onclick="checkAuthStatus()">🔄 检查认证状态</button>
        <button onclick="showDebugInfo()" class="danger">🐛 显示调试信息</button>
    </div>

    <div class="container">
        <h2>🍪 Cookie监听器测试</h2>
        <div class="test-section">
            <h3>Cookie变化模拟器</h3>
            <div class="cookie-simulator">
                <div>
                    <label>域名:</label>
                    <select id="cookieDomain">
                        <option value=".dingtalk.com">✅ .dingtalk.com (正确)</option>
                        <option value="dingtalk.com">❌ dingtalk.com (错误)</option>
                        <option value="oa.dingtalk.com">❌ oa.dingtalk.com (错误)</option>
                        <option value="login.dingtalk.com">❌ login.dingtalk.com (错误)</option>
                    </select>
                </div>
                <div>
                    <label>Cookie名称:</label>
                    <select id="cookieName">
                        <option value="account">✅ account (正确)</option>
                        <option value="login_token">❌ login_token (错误)</option>
                        <option value="session_id">❌ session_id (错误)</option>
                    </select>
                </div>
            </div>
            <div>
                <label>Cookie值:</label>
                <input type="text" id="cookieValue" placeholder="输入模拟的cookie值" 
                       value="oauth_k1%3AmU3S71ux%2FGHrVLxmq9DEsxcyW1mOWrGodfQIyzEuyv1x3YVRbpVu2MxjNtm2JP83AzQcN9hPUG53yxTyae1imPjFBdPDvctNnX8QaW7Kuuw%3D">
            </div>
            <button onclick="simulateCookieChange('added')">➕ 模拟Cookie添加</button>
            <button onclick="simulateCookieChange('changed')">🔄 模拟Cookie变更</button>
            <button onclick="simulateCookieChange('removed')">➖ 模拟Cookie删除</button>
        </div>
        
        <div class="test-section">
            <h3>监听器测试结果</h3>
            <div id="cookieTestResults" class="log-area">等待Cookie变化测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>💾 存储同步测试</h2>
        <div class="test-section">
            <h3>存储键测试</h3>
            <button onclick="testStorageKeys()">🔑 测试存储键一致性</button>
            <button onclick="testStateSync()">🔄 测试状态同步</button>
            <button onclick="clearStorage()">🗑️ 清空存储</button>
            <div id="storageTestResults" class="log-area">等待存储测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>📢 通知机制测试</h2>
        <div class="test-section">
            <h3>UI更新通知</h3>
            <button onclick="testNotificationMechanism()">📡 测试通知机制</button>
            <div id="notificationTestResults" class="log-area">等待通知测试...</div>
        </div>
    </div>

    <div class="container">
        <h2>🐛 调试信息</h2>
        <div class="test-section">
            <div id="debugInfoDisplay" class="debug-info">点击"显示调试信息"按钮查看详细信息</div>
        </div>
    </div>

    <!-- 模拟Chrome API -->
    <script>
        // 模拟Chrome扩展API
        window.chrome = {
            storage: {
                local: {
                    data: {},
                    get: function(keys, callback) {
                        const result = {};
                        if (Array.isArray(keys)) {
                            keys.forEach(key => {
                                if (this.data.hasOwnProperty(key)) {
                                    result[key] = this.data[key];
                                }
                            });
                        } else if (typeof keys === 'string') {
                            if (this.data.hasOwnProperty(keys)) {
                                result[keys] = this.data[keys];
                            }
                        } else if (keys === null || keys === undefined) {
                            Object.assign(result, this.data);
                        }
                        setTimeout(() => callback(result), 10);
                    },
                    set: function(items, callback) {
                        Object.assign(this.data, items);
                        setTimeout(() => {
                            if (callback) callback();
                            // 触发存储变化事件
                            if (this.onChanged && this.onChanged.listeners) {
                                this.onChanged.listeners.forEach(listener => {
                                    listener(items, 'local');
                                });
                            }
                        }, 10);
                    },
                    onChanged: {
                        listeners: [],
                        addListener: function(listener) {
                            this.listeners.push(listener);
                        }
                    }
                }
            },
            cookies: {
                listeners: [],
                onChanged: {
                    addListener: function(listener) {
                        chrome.cookies.listeners.push(listener);
                    }
                }
            },
            runtime: {
                listeners: [],
                sendMessage: function(message, callback) {
                    console.log('📤 发送消息:', message);
                    // 模拟消息处理
                    setTimeout(() => {
                        if (callback) callback({success: true});
                        // 通知所有监听器
                        this.listeners.forEach(listener => {
                            listener(message, {}, () => {});
                        });
                    }, 10);
                },
                onMessage: {
                    addListener: function(listener) {
                        chrome.runtime.listeners.push(listener);
                    }
                }
            }
        };
    </script>
