// 测试用户信息显示修复的脚本
// 这个脚本可以在Chrome扩展的控制台中运行来测试修复效果

console.log('开始测试用户信息显示修复...');

// 测试函数：检查认证状态
async function testAuthStatus() {
  try {
    console.log('1. 测试获取认证状态...');
    
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action: 'getDingTalkAuthStatus' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });

    console.log('认证状态响应:', response);
    
    if (response.success) {
      const data = response.data;
      console.log('✅ 认证状态获取成功');
      console.log('- 是否已认证:', data.isAuthenticated);
      console.log('- 用户信息:', data.userInfo);
      console.log('- 组织数量:', data.organizations?.length || 0);
      console.log('- 选中组织:', data.selectedOrganization?.corpName || '无');
      
      if (data.isAuthenticated) {
        if (data.userInfo && data.userInfo.name && data.userInfo.name !== '钉钉用户') {
          console.log('✅ 用户信息完整');
          return { success: true, userInfoComplete: true, data };
        } else {
          console.log('⚠️ 用户信息不完整或为默认值');
          return { success: true, userInfoComplete: false, data };
        }
      } else {
        console.log('ℹ️ 用户未认证');
        return { success: true, userInfoComplete: false, data };
      }
    } else {
      console.log('❌ 认证状态获取失败:', response.error);
      return { success: false, error: response.error };
    }
    
  } catch (error) {
    console.log('❌ 测试认证状态失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 测试函数：刷新认证状态
async function testRefreshAuth() {
  try {
    console.log('2. 测试刷新认证状态...');
    
    const response = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({ action: 'refreshDingTalkAuth' }, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });

    console.log('刷新认证状态响应:', response);
    
    if (response.success) {
      const data = response.data;
      console.log('✅ 认证状态刷新成功');
      console.log('- 是否已认证:', data.isAuthenticated);
      console.log('- 用户信息:', data.userInfo);
      
      if (data.isAuthenticated && data.userInfo && data.userInfo.name && data.userInfo.name !== '钉钉用户') {
        console.log('✅ 刷新后用户信息完整');
        return { success: true, userInfoComplete: true, data };
      } else if (data.isAuthenticated) {
        console.log('⚠️ 刷新后用户信息仍不完整');
        return { success: true, userInfoComplete: false, data };
      } else {
        console.log('ℹ️ 用户未认证');
        return { success: true, userInfoComplete: false, data };
      }
    } else {
      console.log('❌ 刷新认证状态失败:', response.error);
      return { success: false, error: response.error };
    }
    
  } catch (error) {
    console.log('❌ 测试刷新认证状态失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 测试函数：检查存储数据
async function testStorageData() {
  try {
    console.log('3. 测试检查存储数据...');
    
    const keys = ['Login', 'MY_ORGS_KEY', 'selectedCorpId', 'userSettings', 'dingtalk_auth_timestamp'];
    const data = await chrome.storage.local.get(keys);
    
    console.log('存储数据:', data);
    console.log('- Login状态:', data.Login);
    console.log('- 用户设置:', data.userSettings);
    console.log('- 组织数据:', data.MY_ORGS_KEY);
    console.log('- 选中组织ID:', data.selectedCorpId);
    console.log('- 认证时间戳:', data.dingtalk_auth_timestamp ? new Date(data.dingtalk_auth_timestamp).toLocaleString() : '无');
    
    return { success: true, data };
    
  } catch (error) {
    console.log('❌ 检查存储数据失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 测试函数：检查Cookie
async function testCookieData() {
  try {
    console.log('4. 测试检查Cookie数据...');
    
    const cookies = await chrome.cookies.getAll({ domain: '.dingtalk.com' });
    const accountCookie = cookies.find(c => c.name === 'account');
    
    console.log('Cookie数据:');
    console.log('- 总Cookie数量:', cookies.length);
    console.log('- account Cookie存在:', !!accountCookie);
    
    if (accountCookie) {
      console.log('- account Cookie值长度:', accountCookie.value.length);
      console.log('- account Cookie过期时间:', new Date(accountCookie.expirationDate * 1000).toLocaleString());
    }
    
    return { success: true, hasCookie: !!accountCookie, cookieCount: cookies.length };
    
  } catch (error) {
    console.log('❌ 检查Cookie数据失败:', error.message);
    return { success: false, error: error.message };
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始运行所有测试...\n');
  
  const results = {
    authStatus: await testAuthStatus(),
    refreshAuth: await testRefreshAuth(),
    storageData: await testStorageData(),
    cookieData: await testCookieData()
  };
  
  console.log('\n📊 测试结果汇总:');
  console.log('================');
  
  // 分析结果
  const authWorking = results.authStatus.success;
  const refreshWorking = results.refreshAuth.success;
  const hasValidCookie = results.cookieData.success && results.cookieData.hasCookie;
  const userInfoComplete = results.refreshAuth.userInfoComplete || results.authStatus.userInfoComplete;
  
  console.log('✅ 认证状态获取:', authWorking ? '正常' : '异常');
  console.log('✅ 认证状态刷新:', refreshWorking ? '正常' : '异常');
  console.log('✅ Cookie状态:', hasValidCookie ? '有效' : '无效');
  console.log('✅ 用户信息完整性:', userInfoComplete ? '完整' : '不完整');
  
  if (authWorking && refreshWorking && hasValidCookie && userInfoComplete) {
    console.log('\n🎉 所有测试通过！用户信息显示应该正常工作。');
  } else if (authWorking && refreshWorking && hasValidCookie && !userInfoComplete) {
    console.log('\n⚠️ 认证正常但用户信息不完整，可能需要进一步调试API响应格式。');
  } else if (!hasValidCookie) {
    console.log('\n❌ Cookie无效，用户需要重新登录钉钉。');
  } else {
    console.log('\n❌ 存在问题，需要进一步调试。');
  }
  
  return results;
}

// 如果在浏览器环境中运行，自动执行测试
if (typeof window !== 'undefined' && window.chrome) {
  runAllTests().then(results => {
    console.log('\n测试完成，结果已保存到 window.testResults');
    window.testResults = results;
  }).catch(error => {
    console.error('测试执行失败:', error);
  });
} else {
  console.log('请在Chrome扩展环境中运行此测试脚本');
}

// 导出测试函数供手动调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testAuthStatus,
    testRefreshAuth,
    testStorageData,
    testCookieData,
    runAllTests
  };
}
